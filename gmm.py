#!/usr/bin/env python3
# Two-phase GMM over an entire dataset, using the PER-SAMPLE mask in each folder.
# Output mirrors the input tree under --dst and writes labels.nii.gz per case,
# plus config/mapping_binary.csv (brain=1..K_brain → 1; non-brain → 0).

import argparse, csv, sys, shutil
from pathlib import Path
import numpy as np
import nibabel as nib
from sklearn.mixture import GaussianMixture

# ------------------- constants (kept inside code) -------------------

IMAGE_FILENAME = "image.nii.gz"
MASK_FILENAME  = "mask.nii.gz"
TRUTH_FILENAME = "labels.nii.gz"

# GMM hyperparams
COV  = "full"      # covariance_type
REG  = 1e-6        # reg_covar
SEED = 612385      # random_state

# ------------------- IO helpers -------------------

def load_nii(p: Path):
    img = nib.load(str(p))
    return img.get_fdata(dtype=np.float32), img.affine, img.header

def save_like(arr: np.ndarray, affine, header, out_path: Path, dtype=np.uint16):
    out_path.parent.mkdir(parents=True, exist_ok=True)
    nib.save(nib.Nifti1Image(arr.astype(dtype), affine, header), str(out_path))

def discover_sample_dirs(root: Path, prefix: str | None):
    """Find immediate subfolders that contain required image & mask files."""
    out = []
    for p in sorted([x for x in root.iterdir() if x.is_dir()]):
        if prefix and (prefix not in p.name):
            continue
        if (p / IMAGE_FILENAME).exists() and (p / MASK_FILENAME).exists():
            out.append(p)
    return out

# ------------------- GMM helpers -------------------

def robust_norm(vals: np.ndarray):
    v = vals.astype(np.float32)
    if v.size == 0:
        return v
    p25, p50, p75 = np.percentile(v, [25, 50, 75])
    scale = (p75 - p25) + 1e-6
    return (v - p50) / scale

def fit_gmm(x: np.ndarray, K: int):
    """Fit a 1D GMM on flattened, normalized intensities; return GaussianMixture or None."""
    x = x.reshape(-1, 1)
    n = x.shape[0]
    if n == 0:
        return None
    if float(np.var(x)) < 1e-10:
        return None
    k_eff = min(K, n)  # scikit requires n_components <= n_samples
    if k_eff < 1:
        return None
    gm = GaussianMixture(
        n_components=k_eff,
        covariance_type=COV,
        reg_covar=REG,
        n_init=10,
        random_state=SEED,
        init_params="kmeans",
        max_iter=300,
        tol=1e-4,
    )
    gm.fit(x)
    return gm

# ------------------- core -------------------

def two_phase_gmm_labels(image: np.ndarray,
                         mask_bool: np.ndarray,
                         K_brain: int, K_bg: int):
    """
    Label IDs:
      Brain components     : 1 .. K_brain
      Non-brain components : K_brain+1 .. K_brain+K_bg
    Works with 2D or 3D volumes (we flatten, then reshape back).
    """
    shp = image.shape
    img_flat = image.reshape(-1).astype(np.float32)
    m_flat   = mask_bool.reshape(-1)

    # handle NaN/Inf
    finite = np.isfinite(img_flat)
    if not finite.all():
        med = np.nanmedian(img_flat)
        img_flat = np.nan_to_num(
            img_flat, nan=med,
            posinf=np.max(img_flat[finite]) if finite.any() else 0.0,
            neginf=np.min(img_flat[finite]) if finite.any() else 0.0
        )

    # robust, separate normalization per region
    z = np.zeros_like(img_flat, dtype=np.float32)
    if m_flat.any():
        z[m_flat]  = robust_norm(img_flat[m_flat])
    if (~m_flat).any():
        z[~m_flat] = robust_norm(img_flat[~m_flat])

    labels_flat = np.zeros_like(img_flat, dtype=np.int32)

    # brain GMM
    if m_flat.any() and K_brain > 0:
        gm_b = fit_gmm(z[m_flat], K_brain)
        labels_flat[m_flat] = (gm_b.predict(z[m_flat].reshape(-1,1)) + 1) if gm_b else 1

    # non-brain GMM
    if (~m_flat).any() and K_bg > 0:
        gm_nb = fit_gmm(z[~m_flat], K_bg)
        labels_flat[~m_flat] = (
            gm_nb.predict(z[~m_flat].reshape(-1,1)) + (K_brain + 1)
            if gm_nb else (K_brain + 1)
        )

    return labels_flat.reshape(shp)

def write_gmm_mapping_csv(K_brain: int, K_bg: int, out_csv: Path):
    """Columns: label, mapping, class_name. mapping: 1=brain, 0=non-brain."""
    out_csv.parent.mkdir(parents=True, exist_ok=True)
    with open(out_csv, "w", newline="") as f:
        w = csv.writer(f)
        w.writerow(["label", "mapping", "class_name"])
        for lab in range(1, K_brain + 1):
            w.writerow([lab, 1, f"brain_gmm_{lab}"])
        for j in range(1, K_bg + 1):
            lab = K_brain + j
            w.writerow([lab, 0, f"nonbrain_gmm_{j}"])

def process_case(case_dir: Path, dst_root: Path, K_brain: int, K_bg: int, copy_inputs: bool):
    img_p = case_dir / IMAGE_FILENAME
    msk_p = case_dir / MASK_FILENAME
    if not img_p.exists() or not msk_p.exists():
        print(f"[skip] missing image or mask in: {case_dir}", file=sys.stderr)
        return False

    image, aff, hdr = load_nii(img_p)
    mask,  _,  _    = load_nii(msk_p)

    mask_bool = mask > 0.5

    labels = two_phase_gmm_labels(image=image, mask_bool=mask_bool, K_brain=K_brain, K_bg=K_bg)

    dst_case = dst_root / case_dir.name
    dst_case.mkdir(parents=True, exist_ok=True)

    save_like(labels, aff, hdr, dst_case / TRUTH_FILENAME, dtype=np.uint16)

    if copy_inputs:
        shutil.copy2(img_p, dst_case / IMAGE_FILENAME)
        shutil.copy2(msk_p, dst_case / MASK_FILENAME)

    return True

# ------------------- CLI -------------------

def main():
    ap = argparse.ArgumentParser(description="Two-phase GMM over dataset (per-sample masks; no global mask).")
    ap.add_argument("--src", required=True, type=Path, help="Dataset root containing sample folders")
    ap.add_argument("--dst", required=True, type=Path, help="Output root; mirrors sample folders")
    ap.add_argument("--prefix", default=None, help="Only process folders whose name contains this string (e.g., 'fsm_t1')")
    ap.add_argument("--K_brain", type=int, default=3)
    ap.add_argument("--K_bg",    type=int, default=2)
    ap.add_argument("--copy_inputs", action="store_true", help="Also copy image/mask into --dst case folders")
    ap.add_argument("--mapping_csv", type=Path, default=None, help="Defaults to <dst>/config/mapping_binary.csv")
    args = ap.parse_args()

    cases = discover_sample_dirs(args.src, args.prefix)
    if not cases:
        print(f"No sample folders found under {args.src} (prefix={args.prefix})", file=sys.stderr)
        sys.exit(2)

    args.dst.mkdir(parents=True, exist_ok=True)

    n_ok = 0
    for case in cases:
        n_ok += int(process_case(case, args.dst, args.K_brain, args.K_bg, args.copy_inputs))

    if n_ok == 0:
        print("No cases processed.", file=sys.stderr)
        sys.exit(3)

    mapping_csv = args.mapping_csv or (args.dst / "config" / "mapping_binary.csv")
    write_gmm_mapping_csv(args.K_brain, args.K_bg, mapping_csv)

    print(f"Processed {n_ok}/{len(cases)} cases.")
    print(f"Mapping written to: {mapping_csv}")

if __name__ == "__main__":
    main()
