#!/usr/bin/env python3
# Enhanced two-phase GMM with spatial features for better brain part segmentation.
# Uses intensity + spatial coordinates + optional texture features for clustering.
# Output mirrors the input tree under --dst and writes labels.nii.gz per case,
# plus config/mapping_binary.csv (brain=1..K_brain → 1; non-brain → 0).

import argparse, csv, sys, shutil
from pathlib import Path
import numpy as np
import nibabel as nib
from sklearn.mixture import GaussianMixture
from scipy import ndimage
from sklearn.preprocessing import StandardScaler

# ------------------- constants (kept inside code) -------------------

IMAGE_FILENAME = "image.nii.gz"
MASK_FILENAME  = "mask.nii.gz"
TRUTH_FILENAME = "labels.nii.gz"

# GMM hyperparams
COV  = "full"      # covariance_type
REG  = 1e-6        # reg_covar
SEED = 612385      # random_state

# Feature extraction parameters
USE_SPATIAL_FEATURES = True    # Include x,y,z coordinates
USE_TEXTURE_FEATURES = True    # Include local texture measures
SPATIAL_WEIGHT = 0.3          # Weight for spatial features relative to intensity
TEXTURE_WEIGHT = 0.2          # Weight for texture features relative to intensity

# ------------------- IO helpers -------------------

def load_nii(p: Path):
    img = nib.load(str(p))
    return img.get_fdata(dtype=np.float32), img.affine, img.header

def save_like(arr: np.ndarray, affine, header, out_path: Path, dtype=np.uint16):
    out_path.parent.mkdir(parents=True, exist_ok=True)
    nib.save(nib.Nifti1Image(arr.astype(dtype), affine, header), str(out_path))

def discover_sample_dirs(root: Path, prefix: str | None):
    """Find immediate subfolders that contain required image & mask files."""
    out = []
    for p in sorted([x for x in root.iterdir() if x.is_dir()]):
        if prefix and (prefix not in p.name):
            continue
        if (p / IMAGE_FILENAME).exists() and (p / MASK_FILENAME).exists():
            out.append(p)
    return out

# ------------------- GMM helpers -------------------

def robust_norm(vals: np.ndarray):
    v = vals.astype(np.float32)
    if v.size == 0:
        return v
    p25, p50, p75 = np.percentile(v, [25, 50, 75])
    scale = (p75 - p25) + 1e-6
    return (v - p50) / scale

def extract_spatial_features(shape: tuple, mask_indices: np.ndarray):
    """Extract normalized spatial coordinates for masked voxels."""
    if len(shape) == 2:
        h, w = shape
        y_coords, x_coords = np.meshgrid(np.arange(h), np.arange(w), indexing='ij')
        coords = np.stack([y_coords.ravel(), x_coords.ravel()], axis=1)
    else:  # 3D
        d, h, w = shape
        z_coords, y_coords, x_coords = np.meshgrid(
            np.arange(d), np.arange(h), np.arange(w), indexing='ij'
        )
        coords = np.stack([z_coords.ravel(), y_coords.ravel(), x_coords.ravel()], axis=1)

    # Normalize coordinates to [0, 1] range
    coords = coords.astype(np.float32)
    for i in range(coords.shape[1]):
        coords[:, i] = coords[:, i] / (shape[i] - 1) if shape[i] > 1 else 0

    return coords[mask_indices]

def extract_texture_features(image: np.ndarray, mask_indices: np.ndarray):
    """Extract local texture features using gradient magnitude and local variance."""
    # Check if image is large enough for gradient calculation
    min_size = 3  # Minimum size needed for gradient
    if any(dim < min_size for dim in image.shape):
        # For very small images, return zero texture features
        n_voxels = len(mask_indices)
        return np.zeros((n_voxels, 2), dtype=np.float32)

    try:
        # Compute gradient magnitude
        if len(image.shape) == 2:
            grad_y, grad_x = np.gradient(image)
            grad_mag = np.sqrt(grad_y**2 + grad_x**2)
        else:  # 3D
            grad_z, grad_y, grad_x = np.gradient(image)
            grad_mag = np.sqrt(grad_z**2 + grad_y**2 + grad_x**2)

        # Compute local variance using uniform filter
        from scipy import ndimage
        kernel_size = 3
        local_mean = ndimage.uniform_filter(image.astype(np.float32), size=kernel_size)
        local_var = ndimage.uniform_filter(image.astype(np.float32)**2, size=kernel_size) - local_mean**2
        local_var = np.maximum(local_var, 0)  # Ensure non-negative

        # Stack features
        texture_features = np.stack([grad_mag.ravel(), local_var.ravel()], axis=1)
        return texture_features[mask_indices]

    except (ValueError, IndexError):
        # Fallback for any gradient calculation issues
        n_voxels = len(mask_indices)
        return np.zeros((n_voxels, 2), dtype=np.float32)

def fit_gmm(features: np.ndarray, K: int):
    """Fit a multi-dimensional GMM on feature matrix; return GaussianMixture or None."""
    n, d = features.shape
    if n == 0:
        return None

    # Check for sufficient variance in features
    feature_vars = np.var(features, axis=0)
    valid_dims = feature_vars >= 1e-10

    if not np.any(valid_dims):
        return None

    # Remove low-variance features if any
    if not np.all(valid_dims):
        features = features[:, valid_dims]

    k_eff = min(K, n)  # scikit requires n_components <= n_samples
    if k_eff < 1:
        return None

    try:
        # Try to standardize features
        scaler = StandardScaler()
        features_scaled = scaler.fit_transform(features)

        gm = GaussianMixture(
            n_components=k_eff,
            covariance_type=COV,
            reg_covar=REG,
            n_init=10,
            random_state=SEED,
            init_params="kmeans",
            max_iter=300,
            tol=1e-4,
        )
        gm.fit(features_scaled)
        gm.scaler = scaler  # Store scaler for prediction
        return gm

    except Exception:
        # Fallback: fit GMM without standardization
        try:
            gm = GaussianMixture(
                n_components=k_eff,
                covariance_type=COV,
                reg_covar=REG,
                n_init=10,
                random_state=SEED,
                init_params="kmeans",
                max_iter=300,
                tol=1e-4,
            )
            gm.fit(features)
            # No scaler stored in this case
            return gm
        except Exception:
            return None

# ------------------- core -------------------

def extract_features(image: np.ndarray, mask_indices: np.ndarray, region_name: str):
    """Extract multi-modal features for GMM clustering."""
    shp = image.shape
    img_flat = image.reshape(-1).astype(np.float32)
    n_voxels = len(mask_indices)

    # Start with intensity features
    intensity_features = img_flat[mask_indices].reshape(-1, 1)
    intensity_features = robust_norm(intensity_features.ravel()).reshape(-1, 1)

    feature_list = [intensity_features]

    # Add spatial features if enabled
    if USE_SPATIAL_FEATURES:
        try:
            spatial_features = extract_spatial_features(shp, mask_indices)
            spatial_features *= SPATIAL_WEIGHT  # Scale spatial features
            feature_list.append(spatial_features)
        except Exception:
            # Fallback: add zero spatial features to maintain consistent dimensions
            n_spatial_dims = len(shp)
            zero_spatial = np.zeros((n_voxels, n_spatial_dims), dtype=np.float32)
            feature_list.append(zero_spatial)

    # Add texture features if enabled
    if USE_TEXTURE_FEATURES:
        try:
            texture_features = extract_texture_features(image, mask_indices)
            texture_features *= TEXTURE_WEIGHT  # Scale texture features
            feature_list.append(texture_features)
        except Exception:
            # Fallback: add zero texture features to maintain consistent dimensions
            zero_texture = np.zeros((n_voxels, 2), dtype=np.float32)
            feature_list.append(zero_texture)

    # Combine all features
    combined_features = np.concatenate(feature_list, axis=1)

    return combined_features

def two_phase_gmm_labels(image: np.ndarray,
                         mask_bool: np.ndarray,
                         K_brain: int, K_bg: int):
    """
    Enhanced GMM with spatial and texture features.
    Label IDs:
      Brain components     : 1 .. K_brain
      Non-brain components : K_brain+1 .. K_brain+K_bg
    Works with 2D or 3D volumes.
    """
    shp = image.shape
    img_flat = image.reshape(-1).astype(np.float32)
    m_flat   = mask_bool.reshape(-1)

    # handle NaN/Inf
    finite = np.isfinite(img_flat)
    if not finite.all():
        med = np.nanmedian(img_flat)
        img_flat = np.nan_to_num(
            img_flat, nan=med,
            posinf=np.max(img_flat[finite]) if finite.any() else 0.0,
            neginf=np.min(img_flat[finite]) if finite.any() else 0.0
        )
        # Update the image array for feature extraction
        image = img_flat.reshape(shp)

    labels_flat = np.zeros_like(img_flat, dtype=np.int32)

    # brain GMM with enhanced features
    if m_flat.any() and K_brain > 0:
        brain_indices = np.where(m_flat)[0]
        brain_features = extract_features(image, brain_indices, "Brain region")

        gm_b = fit_gmm(brain_features, K_brain)
        if gm_b and hasattr(gm_b, 'scaler'):
            brain_features_scaled = gm_b.scaler.transform(brain_features)
            labels_flat[m_flat] = gm_b.predict(brain_features_scaled) + 1
        elif gm_b:
            labels_flat[m_flat] = gm_b.predict(brain_features) + 1
        else:
            labels_flat[m_flat] = 1

    # non-brain GMM with enhanced features
    if (~m_flat).any() and K_bg > 0:
        nonbrain_indices = np.where(~m_flat)[0]
        nonbrain_features = extract_features(image, nonbrain_indices, "Non-brain region")

        gm_nb = fit_gmm(nonbrain_features, K_bg)
        if gm_nb and hasattr(gm_nb, 'scaler'):
            nonbrain_features_scaled = gm_nb.scaler.transform(nonbrain_features)
            labels_flat[~m_flat] = gm_nb.predict(nonbrain_features_scaled) + (K_brain + 1)
        elif gm_nb:
            labels_flat[~m_flat] = gm_nb.predict(nonbrain_features) + (K_brain + 1)
        else:
            labels_flat[~m_flat] = K_brain + 1

    return labels_flat.reshape(shp)

def write_gmm_mapping_csv(K_brain: int, K_bg: int, out_csv: Path):
    """Columns: label, mapping, class_name. mapping: 1=brain, 0=non-brain."""
    out_csv.parent.mkdir(parents=True, exist_ok=True)
    with open(out_csv, "w", newline="") as f:
        w = csv.writer(f)
        w.writerow(["label", "mapping", "class_name"])
        for lab in range(1, K_brain + 1):
            w.writerow([lab, 1, f"brain_gmm_{lab}"])
        for j in range(1, K_bg + 1):
            lab = K_brain + j
            w.writerow([lab, 0, f"nonbrain_gmm_{j}"])

def process_case(case_dir: Path, dst_root: Path, K_brain: int, K_bg: int, copy_inputs: bool):
    img_p = case_dir / IMAGE_FILENAME
    msk_p = case_dir / MASK_FILENAME
    if not img_p.exists() or not msk_p.exists():
        print(f"[skip] missing image or mask in: {case_dir}", file=sys.stderr)
        return False

    image, aff, hdr = load_nii(img_p)
    mask,  _,  _    = load_nii(msk_p)

    mask_bool = mask > 0.5

    labels = two_phase_gmm_labels(image=image, mask_bool=mask_bool, K_brain=K_brain, K_bg=K_bg)

    dst_case = dst_root / case_dir.name
    dst_case.mkdir(parents=True, exist_ok=True)

    save_like(labels, aff, hdr, dst_case / TRUTH_FILENAME, dtype=np.uint16)

    if copy_inputs:
        shutil.copy2(img_p, dst_case / IMAGE_FILENAME)
        shutil.copy2(msk_p, dst_case / MASK_FILENAME)

    return True

# ------------------- CLI -------------------

def main():
    ap = argparse.ArgumentParser(description="Two-phase GMM over dataset (per-sample masks; no global mask).")
    ap.add_argument("--src", required=True, type=Path, help="Dataset root containing sample folders")
    ap.add_argument("--dst", required=True, type=Path, help="Output root; mirrors sample folders")
    ap.add_argument("--prefix", default=None, help="Only process folders whose name contains this string (e.g., 'fsm_t1')")
    ap.add_argument("--K_brain", type=int, default=3)
    ap.add_argument("--K_bg",    type=int, default=2)
    ap.add_argument("--copy_inputs", action="store_true", help="Also copy image/mask into --dst case folders")
    ap.add_argument("--mapping_csv", type=Path, default=None, help="Defaults to <dst>/config/mapping_binary.csv")
    args = ap.parse_args()

    cases = discover_sample_dirs(args.src, args.prefix)
    if not cases:
        print(f"No sample folders found under {args.src} (prefix={args.prefix})", file=sys.stderr)
        sys.exit(2)

    args.dst.mkdir(parents=True, exist_ok=True)

    n_ok = 0
    for case in cases:
        n_ok += int(process_case(case, args.dst, args.K_brain, args.K_bg, args.copy_inputs))

    if n_ok == 0:
        print("No cases processed.", file=sys.stderr)
        sys.exit(3)

    mapping_csv = args.mapping_csv or (args.dst / "config" / "mapping_binary.csv")
    write_gmm_mapping_csv(args.K_brain, args.K_bg, mapping_csv)

    print(f"Processed {n_ok}/{len(cases)} cases.")
    print(f"Mapping written to: {mapping_csv}")

if __name__ == "__main__":
    main()
