# labels_counts.py
import os, numpy as np, nibabel as nib

PATH = r"C:\Users\<USER>\Desktop\synth_seg_pipeline\synthstrip_data_v1.5_2d\ixi_t1_136\labels.nii.gz"

if not os.path.isfile(PATH):
    raise FileNotFoundError(PATH)

img = nib.load(PATH)
arr = img.get_fdata().astype(np.int64)   # make sure labels are integers
labels, counts = np.unique(arr, return_counts=True)

# voxel size & volume
voxel_dims = img.header.get_zooms()[:3]              # (dx, dy, dz) in mm
voxel_vol_mm3 = abs(np.linalg.det(img.affine[:3,:3])) or np.prod(voxel_dims)
total = arr.size

print(f"File: {PATH}")
print(f"Voxel dims (mm): {voxel_dims}")
print("\nLabel   Voxels      %     Volume (mm^3)   Volume (mL)")
for lab, cnt in zip(labels, counts):
    pct = 100.0 * cnt / total
    vol_mm3 = cnt * voxel_vol_mm3
    vol_ml = vol_mm3 / 1000.0
    print(f"{int(lab):5d}  {cnt:8d}  {pct:6.2f}   {vol_mm3:13.1f}   {vol_ml:10.3f}")

# summary
print(f"\nTotal unique labels: {labels.size}")
