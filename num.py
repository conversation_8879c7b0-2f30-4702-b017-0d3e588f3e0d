# collect_label_counts.py
import os
import numpy as np
import nibabel as nib

ROOT = r"C:\Users\<USER>\Desktop\synth_seg_pipeline\synthstrip_data_v1.5_2d"
LABEL_NAME = "labels.nii.gz"
INCLUDE_ZERO = True  # set to False to exclude background label 0

counts = []

for name in sorted(os.listdir(ROOT)):
    case_dir = os.path.join(ROOT, name)
    if not os.path.isdir(case_dir):
        continue
    lab_path = os.path.join(case_dir, LABEL_NAME)
    if os.path.isfile(lab_path):
        arr = nib.load(lab_path).get_fdata().astype(np.int64)
        labs = np.unique(arr)
        if not INCLUDE_ZERO:
            labs = labs[labs != 0]
        counts.append(labs.size)
        # optional: show per-case details
        print(f"[OK] {name}: {labs.size} unique labels")

print("\nLabel-count list:", counts)
